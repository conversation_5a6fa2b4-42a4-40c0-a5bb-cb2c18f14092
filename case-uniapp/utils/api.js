// API配置
export const BASE_URL = "http://localhost:7788";

// 请求封装
const request = (options) => {
  const fullUrl = BASE_URL + options.url;
  console.log("发起请求:", {
    url: fullUrl,
    method: options.method || "GET",
    data: options.data || {},
  });

  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method: options.method || "GET",
      data: options.data || {},
      header: {
        "Content-Type": "application/json",
        ...options.header,
      },
      success: (res) => {
        console.log("请求响应:", {
          url: fullUrl,
          statusCode: res.statusCode,
          data: res.data,
        });

        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            console.error("业务错误:", res.data);
            uni.showToast({
              title: res.data.msg || "请求失败",
              icon: "none",
            });
            reject(res.data);
          }
        } else {
          console.error("HTTP错误:", res);
          uni.showToast({
            title: "网络请求失败",
            icon: "none",
          });
          reject(res);
        }
      },
      fail: (err) => {
        console.error("请求失败:", err);
        uni.showToast({
          title: "网络连接失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

// 案例用户相关API
export const caseUserApi = {
  // 获取优秀客户列表
  getExcellentUsers() {
    return request({
      url: "/system/caseUser/mobile/excellent",
    });
  },

  // 获取用户详情
  getUserDetail(userId) {
    return request({
      url: `/system/caseUser/mobile/${userId}`,
    });
  },
};

// 案例信息相关API
export const caseInfoApi = {
  // 获取推荐案例列表
  getRecommendedCases(pageNum = 1, pageSize = 10) {
    const url = `/system/caseInfo/mobile/recommended?pageNum=${pageNum}&pageSize=${pageSize}`;
    console.log("调用推荐案例接口:", url);
    return request({
      url: url,
    });
  },

  // 获取最新案例列表
  getLatestCases(pageNum = 1, pageSize = 10) {
    const url = `/system/caseInfo/mobile/latest?pageNum=${pageNum}&pageSize=${pageSize}`;
    console.log("调用最新案例接口:", url);
    return request({
      url: url,
    });
  },

  // 根据发布人ID获取案例列表
  getCasesByPublisher(publisherId, pageNum = 1, pageSize = 10) {
    const url = `/system/caseInfo/mobile/publisher/${publisherId}?pageNum=${pageNum}&pageSize=${pageSize}`;
    console.log("调用用户案例接口:", url);
    return request({
      url: url,
    });
  },

  // 根据标签获取案例列表
  getCasesByTag(tag) {
    return request({
      url: `/system/caseInfo/mobile/tag/${tag}`,
    });
  },

  // 获取案例详情
  getCaseDetail(caseId) {
    return request({
      url: `/system/caseInfo/mobile/${caseId}`,
    });
  },

  // 增加点击次数
  incrementClick(caseId) {
    return request({
      url: `/system/caseInfo/mobile/click/${caseId}`,
      method: "POST",
    });
  },
};

export default request;
