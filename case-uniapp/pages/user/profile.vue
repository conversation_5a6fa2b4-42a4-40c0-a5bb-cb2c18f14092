<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-info-section" v-if="userInfo">
      <view class="user-header">
        <image class="user-avatar-large" :src="processAvatarUrl(userInfo.avatar)" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name-large">{{ userInfo.nickName }}</text>
          <text class="user-email" v-if="userInfo.email">{{ userInfo.email }}</text>
          <text class="user-phone" v-if="userInfo.phone">{{ userInfo.phone }}</text>
        </view>
      </view>

      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ userCases.length }}</text>
          <text class="stat-label">发布案例</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ totalViews }}</text>
          <text class="stat-label">总浏览量</text>
        </view>
      </view>
    </view>

    <!-- 案例列表 -->
    <view class="cases-section">
      <view class="section-header">
        <text class="section-title">发布的案例</text>
        <text class="case-count">共{{ userCases.length }}个</text>
      </view>

      <view class="case-list" v-if="userCases.length > 0">
        <view class="case-item" v-for="caseItem in userCases" :key="caseItem.caseId">
          <!-- 用户信息 -->
          <view class="case-user" @click="goToUserProfile(caseItem.publisherId)">
            <image class="user-avatar-small" :src="processAvatarUrl(caseItem.publisher.avatar)" mode="aspectFill">
            </image>
            <view class="user-info-small">
              <text class="user-name-small">{{ caseItem.publisher.nickName }}</text>
              <text class="publish-time">{{ formatTime(caseItem.createTime) }}</text>
            </view>
          </view>

          <!-- 案例标签 -->
          <view class="case-tags" v-if="caseItem.caseTags">
            <text class="tag" v-for="tag in processTags(caseItem.caseTags)" :key="tag">#{{ tag }}</text>
          </view>

          <!-- 案例标题 -->
          <view class="case-title" @click="goToCaseDetail(caseItem.caseId)">
            {{ caseItem.caseTitle }}
          </view>

          <!-- 案例内容 -->
          <view class="case-content">
            <view class="content-text" :class="{ expanded: expandedCases[caseItem.caseId] }">
              {{ processCaseContent(caseItem.caseContent, expandedCases[caseItem.caseId] ? 1000 : 150) }}
            </view>
            <text class="expand-btn" v-if="processCaseContent(caseItem.caseContent, 1000).length > 150"
              @click="toggleExpand(caseItem.caseId)">
              {{ expandedCases[caseItem.caseId] ? '收起' : '全文' }}
            </text>
          </view>

          <!-- 案例图片 -->
          <view class="case-images" v-if="caseItem.caseImages">
            <view class="image-grid">
              <view class="image-item" v-for="(image, index) in processImages(caseItem.caseImages).slice(0, 3)"
                :key="index" @click="openImageViewer(processImages(caseItem.caseImages), index)">
                <image class="case-image" :src="image" mode="aspectFill"></image>
                <!-- 显示剩余图片数量 -->
                <view class="more-images" v-if="index === 2 && processImages(caseItem.caseImages).length > 3">
                  <text class="more-count">+{{ processImages(caseItem.caseImages).length - 3 }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 案例操作 -->
          <view class="case-actions">
            <view class="action-item" @click="goToCaseDetail(caseItem.caseId)">
              <text class="action-text">查看详情</text>
            </view>
            <view class="action-item">
              <text class="click-count">{{ caseItem.clickCount }} 次查看</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="userCases.length > 0 && hasMore && !loadingMore">
        <text class="load-text" @click="loadMoreCases">加载更多</text>
      </view>

      <!-- 加载更多中 -->
      <view class="load-more" v-if="loadingMore">
        <text class="load-text loading">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="userCases.length > 0 && !hasMore && !loadingMore">
        <text class="no-more-text">没有更多数据了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else-if="userCases.length === 0 && !loading">
        <image class="empty-image" src="/static/empty-case.png" mode="aspectFit"></image>
        <text class="empty-text">暂无发布的案例</text>
      </view>
    </view>

    <!-- 图片查看器 -->
    <ImageViewer :visible="imageViewerVisible" :images="currentImages" :initialIndex="currentImageIndex"
      @close="closeImageViewer" @change="onImageChange" />

    <!-- 加载中 -->
    <view class="loading" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
import { caseUserApi, caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processAvatarUrl, processTags, processCaseContent, previewImages } from '@/utils/utils.js'
import ImageViewer from '@/components/ImageViewer.vue'

export default {
  components: {
    ImageViewer
  },
  data() {
    return {
      userId: null,
      userInfo: null,
      userCases: [],
      loading: false,
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      hasMore: true,
      loadingMore: false,
      // 展开的案例内容
      expandedCases: {},
      // 图片查看器相关
      imageViewerVisible: false,
      currentImages: [],
      currentImageIndex: 0
    }
  },

  computed: {
    // 计算总浏览量
    totalViews() {
      return this.userCases.reduce((total, caseItem) => total + (caseItem.clickCount || 0), 0)
    }
  },

  onLoad(options) {
    this.userId = options.userId
    if (this.userId) {
      this.loadUserData()
    }
  },

  onPullDownRefresh() {
    this.refreshData().then(() => {
      uni.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    this.loadMoreCases()
  },

  methods: {
    // 加载用户数据
    async loadUserData() {
      this.loading = true
      try {
        // 先加载用户信息，再加载用户案例（因为案例需要用到用户信息）
        await this.loadUserInfo()
        await this.loadUserCases()
      } catch (error) {
        console.error('加载用户数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 刷新数据
    async refreshData() {
      this.currentPage = 1
      this.hasMore = true
      this.userCases = []
      this.expandedCases = {}
      await this.loadUserData()
    },

    // 加载更多案例
    async loadMoreCases() {
      if (!this.hasMore || this.loading || this.loadingMore) return

      console.log('加载更多用户案例，当前页码:', this.currentPage, '下一页:', this.currentPage + 1)
      this.loadingMore = true
      this.currentPage++

      try {
        await this.loadUserCases()
      } catch (error) {
        // 如果加载失败，回退页码
        this.currentPage--
        console.error('加载更多用户案例失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loadingMore = false
      }
    },

    // 加载用户信息
    async loadUserInfo() {
      try {
        const res = await caseUserApi.getUserDetail(this.userId)
        this.userInfo = res.data

        // 设置页面标题
        uni.setNavigationBarTitle({
          title: this.userInfo.nickName + '的主页'
        })
      } catch (error) {
        console.error('加载用户信息失败:', error)
        uni.showToast({
          title: '用户不存在',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },

    // 加载用户案例
    async loadUserCases() {
      // 如果是加载更多，检查loadingMore状态；如果是第一页且不是从loadUserData调用，检查loading状态
      const isFirstPage = this.currentPage === 1
      if (!isFirstPage && this.loadingMore) return

      // 只有在独立调用loadUserCases时才设置loading状态
      const shouldSetLoading = isFirstPage && !this.loading
      if (shouldSetLoading) {
        this.loading = true
      }

      try {
        const res = await caseInfoApi.getCasesByPublisher(this.userId, this.currentPage, this.pageSize)
        const cases = res.data || []

        // 为每个案例添加发布者信息
        const processedCases = cases.map(caseItem => ({
          ...caseItem,
          publisher: this.userInfo || {
            nickName: '未知用户',
            avatar: ''
          },
          publisherId: this.userId
        }))

        if (this.currentPage === 1) {
          this.userCases = processedCases
        } else {
          this.userCases = [...this.userCases, ...processedCases]
        }

        // 判断是否还有更多数据
        this.hasMore = cases.length >= this.pageSize

        console.log(`加载用户案例${isFirstPage ? '第一页' : '更多'}数据完成，当前页码: ${this.currentPage}, 获取数据量: ${cases.length}, 是否还有更多: ${this.hasMore}`)
      } catch (error) {
        console.error('加载用户案例失败:', error)
        if (this.currentPage === 1) {
          this.userCases = []
        }
        this.hasMore = false
        throw error // 重新抛出错误，让调用方处理
      } finally {
        if (shouldSetLoading) {
          this.loading = false
        }
      }
    },

    // 切换内容展开状态
    toggleExpand(caseId) {
      this.$set(this.expandedCases, caseId, !this.expandedCases[caseId])
    },

    // 跳转到用户主页
    goToUserProfile(userId) {
      uni.navigateTo({
        url: `/pages/user/profile?userId=${userId}`
      })
    },

    // 跳转到案例详情
    goToCaseDetail(caseId) {
      uni.navigateTo({
        url: `/pages/case/detail?caseId=${caseId}`
      })
    },

    // 打开图片查看器
    openImageViewer(images, index = 0) {
      this.currentImages = images
      this.currentImageIndex = index
      this.imageViewerVisible = true
    },

    // 关闭图片查看器
    closeImageViewer() {
      this.imageViewerVisible = false
      this.currentImages = []
      this.currentImageIndex = 0
    },

    // 图片切换事件
    onImageChange(index) {
      this.currentImageIndex = index
    },

    // 工具方法
    formatTime,
    processImages,
    processAvatarUrl,
    processTags,
    processCaseContent,
    previewImages
  }
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-info-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar-large {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.user-name-large {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.user-email,
.user-phone {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 案例列表区域 */
.cases-section {
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.case-count {
  font-size: 26rpx;
  color: #999;
}

/* 案例列表 */
.case-list {
  margin-bottom: 20rpx;
}

.case-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.case-user {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info-small {
  flex: 1;
}

.user-name-small {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.case-tags {
  margin-bottom: 15rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007AFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
  margin-bottom: 8rpx;
}

.case-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 15rpx;
}

.case-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-text:not(.expanded) {
  -webkit-line-clamp: 3;
}

.expand-btn {
  color: #007AFF;
  font-size: 26rpx;
  margin-top: 10rpx;
  display: inline-block;
}

/* 案例图片 */
.case-images {
  margin-bottom: 20rpx;
}

.image-grid {
  display: flex;
  gap: 10rpx;
}

.image-item {
  position: relative;
  flex: 1;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  height: 100%;
}

.more-images {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-count {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

/* 案例操作 */
.case-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
}

.action-text {
  color: #007AFF;
  font-size: 26rpx;
}

.click-count {
  color: #999;
  font-size: 24rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.load-text {
  color: #007AFF;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: 1rpx solid #007AFF;
  border-radius: 50rpx;
  background-color: #fff;
}

.load-text:active {
  background-color: #f0f8ff;
}

.load-text.loading {
  color: #999;
  border-color: #ddd;
  background-color: #f5f5f5;
}

/* 没有更多数据 */
.no-more {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.no-more-text {
  color: #999;
  font-size: 26rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
</style>
