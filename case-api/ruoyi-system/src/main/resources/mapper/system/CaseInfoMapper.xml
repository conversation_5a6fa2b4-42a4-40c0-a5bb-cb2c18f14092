<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CaseInfoMapper">
    
    <resultMap type="CaseInfo" id="CaseInfoResult">
        <id     property="caseId"         column="case_id"         />
        <result property="caseTitle"      column="case_title"      />
        <result property="caseContent"    column="case_content"    />
        <result property="caseImages"     column="case_images"     />
        <result property="templateType"   column="template_type"   />
        <result property="caseTags"       column="case_tags"       />
        <result property="clickCount"     column="click_count"     />
        <result property="publisherId"    column="publisher_id"    />
        <result property="isRecommended"  column="is_recommended"  />
        <result property="status"         column="status"          />
        <result property="delFlag"        column="del_flag"        />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
        <association property="publisher" javaType="CaseUser" resultMap="publisherResult" />
    </resultMap>

    <resultMap type="CaseUser" id="publisherResult">
        <id     property="userId"       column="publisher_user_id"      />
        <result property="nickName"     column="publisher_nick_name"    />
        <result property="avatar"       column="publisher_avatar"       />
        <result property="phone"        column="publisher_phone"        />
        <result property="email"        column="publisher_email"        />
        <result property="sex"          column="publisher_sex"          />
    </resultMap>

    <sql id="selectCaseInfoVo">
        select c.case_id, c.case_title, c.case_content, c.case_images, c.template_type, c.case_tags, c.click_count, c.publisher_id, c.is_recommended, c.status, c.del_flag, c.create_by, c.create_time, c.update_by, c.update_time, c.remark,
               u.user_id as publisher_user_id, u.nick_name as publisher_nick_name, u.avatar as publisher_avatar, u.phone as publisher_phone, u.email as publisher_email, u.sex as publisher_sex
        from case_info c
        left join case_user u on c.publisher_id = u.user_id
    </sql>

    <select id="selectCaseInfoList" parameterType="CaseInfo" resultMap="CaseInfoResult">
        <include refid="selectCaseInfoVo"/>
        where c.del_flag = '0'
        <if test="caseId != null and caseId != 0">
            AND c.case_id = #{caseId}
        </if>
        <if test="caseTitle != null and caseTitle != ''">
            AND c.case_title like concat('%', #{caseTitle}, '%')
        </if>
        <if test="templateType != null and templateType != ''">
            AND c.template_type = #{templateType}
        </if>
        <if test="caseTags != null and caseTags != ''">
            AND c.case_tags like concat('%', #{caseTags}, '%')
        </if>
        <if test="publisherId != null and publisherId != 0">
            AND c.publisher_id = #{publisherId}
        </if>
        <if test="isRecommended != null and isRecommended != ''">
            AND c.is_recommended = #{isRecommended}
        </if>
        <if test="status != null and status != ''">
            AND c.status = #{status}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(c.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(c.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
        </if>
        order by c.create_time desc
    </select>
    
    <select id="selectRecommendedCases" resultMap="CaseInfoResult">
        <include refid="selectCaseInfoVo"/>
        where c.del_flag = '0' and c.status = '0' and c.is_recommended = 'Y'
        order by c.create_time desc
    </select>
    
    <select id="selectLatestCases" resultMap="CaseInfoResult">
        <include refid="selectCaseInfoVo"/>
        where c.del_flag = '0' and c.status = '0'
        order by c.create_time desc
    </select>
    
    <select id="selectCaseInfoByCaseId" parameterType="Long" resultMap="CaseInfoResult">
        <include refid="selectCaseInfoVo"/>
        where c.case_id = #{caseId}
    </select>
    
    <select id="selectCaseInfoByPublisherId" parameterType="Long" resultMap="CaseInfoResult">
        <include refid="selectCaseInfoVo"/>
        where c.del_flag = '0' and c.status = '0' and c.publisher_id = #{publisherId}
        order by c.create_time desc
    </select>
    
    <select id="selectCaseInfoByTag" parameterType="String" resultMap="CaseInfoResult">
        <include refid="selectCaseInfoVo"/>
        where c.del_flag = '0' and c.status = '0' and c.case_tags like concat('%', #{tag}, '%')
        order by c.create_time desc
    </select>
    
    <select id="checkCaseTitleUnique" parameterType="String" resultMap="CaseInfoResult">
        select case_id, case_title from case_info where case_title = #{caseTitle} and del_flag = '0' limit 1
    </select>
    
    <insert id="insertCaseInfo" parameterType="CaseInfo" useGeneratedKeys="true" keyProperty="caseId">
        insert into case_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseTitle != null and caseTitle != ''">case_title,</if>
            <if test="caseContent != null and caseContent != ''">case_content,</if>
            <if test="caseImages != null and caseImages != ''">case_images,</if>
            <if test="templateType != null and templateType != ''">template_type,</if>
            <if test="caseTags != null and caseTags != ''">case_tags,</if>
            <if test="clickCount != null">click_count,</if>
            <if test="publisherId != null">publisher_id,</if>
            <if test="isRecommended != null and isRecommended != ''">is_recommended,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="caseTitle != null and caseTitle != ''">#{caseTitle},</if>
            <if test="caseContent != null and caseContent != ''">#{caseContent},</if>
            <if test="caseImages != null and caseImages != ''">#{caseImages},</if>
            <if test="templateType != null and templateType != ''">#{templateType},</if>
            <if test="caseTags != null and caseTags != ''">#{caseTags},</if>
            <if test="clickCount != null">#{clickCount},</if>
            <if test="publisherId != null">#{publisherId},</if>
            <if test="isRecommended != null and isRecommended != ''">#{isRecommended},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateCaseInfo" parameterType="CaseInfo">
        update case_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseTitle != null and caseTitle != ''">case_title = #{caseTitle},</if>
            <if test="caseContent != null">case_content = #{caseContent},</if>
            <if test="caseImages != null">case_images = #{caseImages},</if>
            <if test="templateType != null and templateType != ''">template_type = #{templateType},</if>
            <if test="caseTags != null">case_tags = #{caseTags},</if>
            <if test="clickCount != null">click_count = #{clickCount},</if>
            <if test="publisherId != null">publisher_id = #{publisherId},</if>
            <if test="isRecommended != null and isRecommended != ''">is_recommended = #{isRecommended},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where case_id = #{caseId}
    </update>

    <update id="incrementClickCount" parameterType="Long">
        update case_info set click_count = click_count + 1 where case_id = #{caseId}
    </update>

    <delete id="deleteCaseInfoByCaseId" parameterType="Long">
        update case_info set del_flag = '2' where case_id = #{caseId}
    </delete>

    <delete id="deleteCaseInfoByCaseIds" parameterType="String">
        update case_info set del_flag = '2' where case_id in 
        <foreach item="caseId" collection="array" open="(" separator="," close=")">
            #{caseId}
        </foreach>
    </delete>

</mapper>
